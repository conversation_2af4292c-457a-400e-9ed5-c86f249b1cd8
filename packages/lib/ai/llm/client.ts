import { AzureChatOpenAI, ChatOpenAI } from '@langchain/openai'
import { ChatAlibabaTongyi } from '@langchain/community/chat_models/alibaba_tongyi'
import { Config } from '../../../config'

export const OPENAI_MODELS = ['gpt-5', 'gpt-5-chat', 'gpt-5-mini'] as const
export type OpenAIModelName = typeof OPENAI_MODELS[number]

interface IOpenAIInitParams {
  model?: OpenAIModelName
  maxTokens?: number
  reasoningEffort?: string,
  timeout?: number
}

export const CLIENT_DEFAULT_TIMEOUT = 2 * 60 * 1000  // 2 minutes

export class AzureOpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): AzureChatOpenAI {
    const {
      model = 'gpt-5-mini',
      maxTokens = 512,
      reasoningEffort = 'minimal', // 'minimal'|'low'|'medium'|'high'（GPT-5 额外支持 'minimal'）
      timeout = Math.max(1000, CLIENT_DEFAULT_TIMEOUT),
    } = params

    const isReasoningModel = [
      'gpt-5',
      'gpt-5-mini',
      'gpt-5-nano',
    ].includes(model)

    const fields: any = {
      model,
      timeout,
      // Azure 认证与端点
      azureOpenAIApiKey: Config.setting.azureOpenAI.azureOpenAIApiKey,
      azureOpenAIApiInstanceName: Config.setting.azureOpenAI.azureOpenAIApiInstanceName,
      azureOpenAIApiDeploymentName: model,
      azureOpenAIApiVersion: Config.setting.azureOpenAI.azureOpenAIApiVersion,
      azureOpenAIBasePath: Config.setting.azureOpenAI.apiBaseUrl,
      maxRetries: 1,
      useResponsesApi: true,
      // configuration: { logLevel: 'debug' },  // OpenAI SDK 将打印请求/响应头与 body（敏感内容会部分打码）
    }

    if (isReasoningModel) {
      fields.maxCompletionTokens = Math.max(1, maxTokens + 256)
      fields.reasoning = { 'effort': reasoningEffort }
    } else {
      fields.maxTokens = maxTokens
    }
    return new AzureChatOpenAI(fields)
  }
}

export class CheapOpenAI {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const {
      model = 'gpt-5-mini',
      maxTokens = 512,
      reasoningEffort = 'minimal', // 'minimal'|'low'|'medium'|'high'（GPT-5 额外支持 'minimal'）
      timeout = Math.max(1000, CLIENT_DEFAULT_TIMEOUT),
    } = params

    const isReasoningModel = [
      'gpt-5',
      'gpt-5-mini',
      'gpt-5-nano',
    ].includes(model)

    const openaiConfig: any = {
      model,
      timeout,
      apiKey: Config.setting.cheapOpenAI.apiKey,
      maxRetries: 1,
      useResponsesApi: true,
      configuration: { baseURL: Config.setting.cheapOpenAI.apiBaseUrl },
    }

    if (isReasoningModel) {
      openaiConfig.maxCompletionTokens = Math.max(1, maxTokens + 256)
      openaiConfig.reasoning = { 'effort': reasoningEffort }
    } else {
      openaiConfig.maxTokens = maxTokens
    }
    return new ChatOpenAI(openaiConfig)
  }
}

export class OpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const {
      model = 'gpt-5-mini',
      maxTokens = 1024,
      reasoningEffort = 'minimal', // 'minimal'|'low'|'medium'|'high'（GPT-5 额外支持 'minimal'）
      timeout = Math.max(1000, CLIENT_DEFAULT_TIMEOUT),
    } = params

    const isReasoningModel = [
      'gpt-5',
      'gpt-5-mini',
      'gpt-5-nano',
    ].includes(model)

    const openaiConfig: any = {
      model,
      timeout,
      // OpenAI 认证与端点
      apiKey: Config.setting.openai.apiKeys[0],
      configuration: { baseURL: Config.setting.openai.apiBaseUrl },
      maxRetries: 1,
    }

    if (isReasoningModel) {
      openaiConfig.maxCompletionTokens = Math.max(1, maxTokens + 256)
      openaiConfig.reasoning = { 'effort': reasoningEffort }
    } else {
      openaiConfig.maxTokens = maxTokens
    }
    return new ChatOpenAI(openaiConfig)
  }
}

export class StableClaude {
  public static getClient(model = 'claude-3-5-sonnet-20241022'): ChatOpenAI {
    return new ChatOpenAI({
      model: model,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      maxRetries: 1,
      openAIApiKey: Config.setting.stableClaude.apiKey,
      configuration: {
        baseURL: Config.setting.stableClaude.apiBaseUrl
      },
    })
  }
}

export class MiTaAI {
  public static getClient(model: 'concise' | 'detail' | 'research'): ChatOpenAI {
    return new ChatOpenAI({
      model: model,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: 'http://112.124.32.162:8000/v1',
        defaultHeaders: {
          Authorization: 'Bearer 61638845b28fa859c374a79f-0abc662597fb4d4ca498a786cbffb761'
        }
      },
    })
  }
}

export class QwenMax {
  public static getClient() {
    return new ChatAlibabaTongyi({
      model: 'qwen-max',
      alibabaApiKey: Config.setting.qwen.apiKey,
    })
  }
}

export class PerplexityAI {
  public static getClient() {
    return new ChatOpenAI({
      model: 'llama-3-sonar-large-32k-online',
      timeout: CLIENT_DEFAULT_TIMEOUT,
      openAIApiKey: '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',
      configuration: {
        baseURL: 'https://api.perplexity.ai'
      },
    })
  }
}