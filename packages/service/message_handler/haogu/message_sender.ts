import { Config } from 'config'
import { ChatHistoryService } from '../../chat_history/chat_history'
import logger from 'model/logger/logger'
import { UUID } from 'lib/uuid/uuid'
import { Haogu<PERSON><PERSON> } from 'model/haogu/crm/client'
import { HaoguCanSendMessage, HaoguEmotionMessage, HaoguFileMessage, HaoguImageMessage, HaoguLinkMessage, HaoguMediaMessage, HaoguMessageType, HaoguVideoChannelMessage, HaoguVideoMessage } from './type'


interface HaoguSendMsg {
    chat_id: string
    toolUserId:string
    conversationId:string
    ai_msg: string // 放到聊天记录中的文本信息，在发送纯文本情况下与 send_msg 相同。 当发送文件或资料时，此处有可能是占位符，可以单独设置 send_msg 为 [xx文件.txt] 之类。
    type: HaoguMessageType
    send_msg?: HaoguCanSendMessage
}

interface HaoguSendOptions {
  shortDes?: string // 简短描述
  round_id?: string // 轮次 id, 用于记录模型输出
  sop_id?: string
}

export class HaoguMessageSender {
  client: HaoguApi
  chatHistoryServiceClient:ChatHistoryService
  constructor(client:HaoguApi, chatHistoryServiceClient:ChatHistoryService) {
    this.client = client
    this.chatHistoryServiceClient = chatHistoryServiceClient
  }
  public async sendById(msg: HaoguSendMsg, options?: HaoguSendOptions) {
    if (msg.chat_id.split('_').length != 2) {
      logger.warn(`发送haogu消息的时候chat_id格式不对,chatId:${msg.chat_id}`)
      return
    }

    const messageId:string = `mock_${UUID.short()}`

    if (Config.setting.localTest) {
      // add Bot Message 中会 Print 消息
    } else {
      if (!msg.send_msg) {
        await this.client.sendTextMessage(
          msg.toolUserId, msg.ai_msg, msg.conversationId, messageId
        )
      } else if (msg.type == HaoguMessageType.text) {
        await this.client.sendTextMessage(
          msg.toolUserId, msg.ai_msg, msg.conversationId, messageId
        )
      } else if (msg.type == HaoguMessageType.image) {
        const message = msg.send_msg as HaoguImageMessage
        await this.client.sendImageMessage(msg.toolUserId, msg.conversationId, messageId, message.fileUrl, message.fileSize, message.imageWidth, message.imageHeight, message.md5)
      } else if (msg.type == HaoguMessageType.emotion) {
        const message = msg.send_msg as HaoguEmotionMessage
        await this.client.sendEmotionMessage(msg.toolUserId, msg.conversationId, messageId, message.fileUrl, message.type)
      } else if (msg.type == HaoguMessageType.video) {
        const message = msg.send_msg as HaoguVideoMessage
        await this.client.sendVideoMessage(msg.toolUserId, msg.conversationId, messageId, message.fileUrl, message.fileSize, message.imageWidth, message.imageHeight, message.md5)
      } else if (msg.type == HaoguMessageType.file) {
        const message = msg.send_msg as HaoguFileMessage
        await this.client.sendFileMessage(msg.toolUserId, msg.conversationId, messageId, message.fileUrl, message.fileSize, message.md5)
      } else if (msg.type == HaoguMessageType.link) {
        const message = msg.send_msg as HaoguLinkMessage
        await this.client.sendLinkMessage(msg.toolUserId, msg.conversationId, messageId, message.title, message.desc, message.url, message.imageUrl)
      } else if (msg.type == HaoguMessageType.videoChannel) {
        const message = msg.send_msg as HaoguVideoChannelMessage
        await this.client.sendMpvVideoMessage(msg.toolUserId, msg.conversationId, messageId, message.mpvVideoId)
      } else if (msg.type == HaoguMessageType.media) {
        const message = msg.send_msg as HaoguMediaMessage
        await this.client.sendMediaMessage(msg.toolUserId, msg.conversationId, messageId, message.mediaId)
      } else {
        logger.warn(`未知的类型: ${msg.type}`)
      }
    }

    if (options?.shortDes && !options?.shortDes.startsWith('[')) {
      options.shortDes = `[${options.shortDes}]`
    }

    await this.chatHistoryServiceClient.addBotMessage(msg.chat_id, msg.ai_msg, options?.shortDes, { message_id:messageId, round_id: options?.round_id, sop_id:options?.sop_id, state:'sent' })
  }
}
