import logger from 'model/logger/logger'
import { ChatHistoryService } from '../chat_history/chat_history'
import { EventTracker } from 'model/logger/data_driven'
import { FreeThink } from './freethink'
import { getPrompt } from './prompt'
import { IWorkflowState } from '../llm/state'
import { LLM } from 'lib/ai/llm/llm_model'
import { Reply } from './reply'
import { StageFilter } from './stage'
import { TaskManager } from '../task/task_manager'
import { trackInvoke } from './workflow'

export enum Node { FreeTalk = 'free_talk' }

export class FreeTalk {
  private readonly chatHistoryService: ChatHistoryService
  private readonly contextBuilder: any
  private freeThink: FreeThink
  private replyClient: Reply
  private stageFilter: StageFilter
  private userLanguage: string
  constructor(
    chatHistoryService: ChatHistoryService,
    contextBuilder: any,
    eventTracker: EventTracker,
    replyClient: Reply,
    stageFilter: StageFilter,
    userLanguage?: string,
  ) {
    this.chatHistoryService = chatHistoryService
    this.contextBuilder = contextBuilder
    this.freeThink = new FreeThink(chatHistoryService, eventTracker)
    this.replyClient = replyClient
    this.stageFilter = stageFilter
    this.userLanguage = userLanguage ?? ''
  }

  @trackInvoke
  public async invoke(state: IWorkflowState) {
    const metaActionStage = await this.stageFilter.getStageInfo(state.chat_id)
    const contextComponent = new this.contextBuilder({ state })

    const { action, strategy, task } = await this.freeThink.invoke(
      state,
      metaActionStage.thinkPrompt,
      metaActionStage.metaActions,
      await contextComponent.customerBehavior(state.chat_id),
      await contextComponent.customerPortrait(state.chat_id),
      await contextComponent.temporalInformation(state.chat_id),
    )

    await TaskManager.addPassiveTasks(state.chat_id, task, state.round_id)  // 添加任务

    const actionInfo = await this.stageFilter.handleAction(state.chat_id, state.round_id, strategy, action)
    if (actionInfo.guidance === '结束后续回复') {
      return Node.FreeTalk
    }

    const talkStrategyPrompt = [strategy, actionInfo.guidance, this.userLanguage].filter(Boolean).join('\n')

    // build the context
    const composerPrompt = await getPrompt('free-compose')
    const dialogHistory = await this.chatHistoryService.getDialogHistory(state.chat_id, 3, 9)
    const output = await LLM.predict(
      composerPrompt, {
        responseJSON: true,
        meta: {
          promptName: 'composer',
          chat_id: state.chat_id,
          round_id: state.round_id,
        } }, {
        dialogHistory: dialogHistory,
        talkStrategy: strategy,
      })

    let module: string[] = []
    try {
      const parsedOutput = JSON.parse(output)
      module = parsedOutput.module
    } catch (error) {
      logger.error('Composer 解析 JSON 失败:', error)
    }

    const context = await this.contextBuilder.build({
      state,
      courseConfig: module.includes('课程设置'),
      retrievedKnowledge: module.includes('补充知识'),
      customerMemory: module.includes('客户记忆'),
      customerBehavior: module.includes('客户行为'),
      customerPortrait: module.includes('客户画像'),
      temporalInformation: module.includes('时间信息'),
      talkStrategyPrompt: talkStrategyPrompt,
    })

    await this.replyClient.invoke({
      state,
      model: 'gpt-5-chat',
      context: context,
      promptName: 'free_talk',
      postReplyCallBack: actionInfo.callback
    })

    // 标记任务完成
    await TaskManager.checkAndMarkTasksCompleted(state.chat_id, state.round_id, this.chatHistoryService)
    return Node.FreeTalk
  }
}