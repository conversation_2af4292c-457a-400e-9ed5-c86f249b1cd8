// ====== API 类型定义（仅覆盖本 client 用到的接口） ======
export interface CommonParams { reqTime: number }
export interface PageParams { pageNo?: number; pageSize?: number }
export interface PageParamsCurrentSize { current?: number; size?: number }

// 1) 查询类
export interface QueryAiConversationStaffListItem {
  staff_id: number | string;
  staff_tool_user_id: string;
}
export type QueryAiConversationStaffListRes = QueryAiConversationStaffListItem[];

export interface GetCustByStaffIdReq extends PageParams { staffId: number | string }
export interface GetCustByStaffIdItem {
  staff_id: number | string;
  cust_tool_user_id: string;
  cust_unified_user_id: number | string;
  cust_union_id?: string;
  nick_name?: string;
}
export type GetCustByStaffIdRes = GetCustByStaffIdItem[];

export interface GetCustomInfoReq extends PageParams { custUnifiedUserId: number | string }
export type GetCustomInfoItem = unknown
export interface GetCustomInfoRes {
  basicUnifiedUserFeignDTO: {
    unifiedUserId: number;
    avatar: string;
    inServerTime: boolean;
    complained: boolean;
    refunded: boolean;
    tags: string[];
  };
  appropriatenessManageFeignDTO: {
    unifiedUserId: number;
    gender: number;
    age: number;
    educationAttainment: string;
    career: string;
    device: string;
    address: string;
    asset: string[];
    investigate: {
      duration: string;
      ballpark: string;
      varieties: string;
      preference: string;
      objective: string;
      knowledge: string;
    };
    riskTolerance: string;
    riskAssessments: { createTime: number; inServer: boolean; tolerance: string; surveyId: string; }[];
  };
  userOrderList: {
    orderType: number;
    orderId: string;
    payDate: string;
    courseName: string;
    actualPayment: string;
    salesmanName: string;
    salesmanDept2Name: string;
    salesmanWxName: string;
    salesmanPhone: string;
    corpId: string;
    workwxUserId: string;
    workwxName: string;
    encryptMobile: string;
    courseValidDay: string;
    benAccount: string;
    payMethod: string;
    coursePrice: string;
    couponAmount: string;
    payment: string;
    unPayment: string;
    payFlowList: { relaFlowId: string; payDate: string; payMethod: string; payment: string; payChannel: string; flowStatus: string; paymentVoucher: string; remarks: string; }[];
    signType: string;
    signStatus: string;
    contractDate: string;
    contractNo: string;
    serverStartTime: string;
    serverEndTime: string;
    jsSurveyId: string;
    combinationCourseList: { quotaNo: string; quotaName: string; validMonth: string; serviceCalType: string; serverStartTime: string; serverEndTime: string; openingPeriod: string; authorityStatus: string; }[];
    questionAnswerList: { answerDate: string; answerType: string; answerResult: string; answerAttach: string; }[];
  }[];
  userTrafficTrajectory: {
    unifiedUserId: number;
    userTrafficTrajectories: { commodityName: string; salesmanName: string; trajectoryTime: number; flagWx: string; }[];
    userLiveLearnTrajectories: { title: string; trajectoryTime: string; learningDuration: string; watchLiveDuration: string; hisLearnedTime: string; }[];
    userOrderTrajectories: { trajectoryTime: number; courseName: string; orderAmount: number; salesman: string; }[];
    userAppTrajectories: { trajectoryTime: number; platform: string; logoutTime?: number; timeLength: string; }[];
    userVIPCourseTrajectories: { title: string; trajectoryTime: string; learningDuration: string; watchLiveDuration: string; hisLearnedTime: string; }[];
  };
}

export interface AtItem { nickname?: string; userId?: string | number }
export enum MessageType { Text = 11041, Image = 11042, Video = 11043, Voice = 11044, File = 11045, Location = 11046, Link = 11047, Emoji = 11048, RedPacket = 11049, BusinessCard = 11050, MiniProgram = 11066, Channels = 101010, Sticker = 110490 }
export interface ChatMessage {
  mock_id?: string; at_list?: AtItem[]; content?: string; type?: MessageType | number; content_type?: string | number;
  conversation_id?: string; search_conversation_id?: string; is_download?: number | boolean; local_id?: string | number; receiver?: string | number;
  send_time?: number; msg_time?: number; sender?: string | number; server_id?: string | number; file_size?: number; file_path?: string; file_name?: string;
  duration?: number; width?: number; height?: number; source?: string; user_id?: string | number; avatar?: string; nickname?: string; name?: string; url?: string;
  image_url?: string; title?: string; desc?: string; packet_id?: string; remark?: string; money?: number; enter_point?: string; headimg?: string;
  image_key1?: string; image_key2?: string; image_key3?: string; image_size?: number; is_withdraw?: number; is_room?: number; is_staff?: number;
  link_is_read?: number; link_read_info_ext?: string; extras?: string; thumb_url?: string; cover_url?: string; voice_time?: number; md5?: string;
  weapp_icon_url?: string; appid?: string; username?: string; is_delete?: number; delete_time?: number; cloud_custom_data?: string;
}
export type QueryChatPageRes = ChatMessage[]

// 2) 标签
export interface ResCustomerParamOption { id: number; customerParamId: number; optionValue: string; optionsKey: string; sort: number }
export interface ResCustomerParam { id: number; paramName: string; paramType: string; paramKey: string; optionList: ResCustomerParamOption[] }
export interface GetCustomerLabelRes { chatLabels: ResCustomerParam[]; salesLabels: ResCustomerParam[]; wecomLabels: ResCustomerParam[]; systemLabels: ResCustomerParam[] }
export interface GetCustomerAILabelRes { staffId: number; list: { custUnifiedUserId: number; labels: ResCustomerParamOption[] }[] }
export interface MarkAILabelRes { staffId: number; custUnifiedUserId: number; labels: ResCustomerParamOption[] }

// 3) 发送消息
export type SendTextMessageRes = Record<string, never>
export type SendImageMessageRes = Record<string, never>
export type SendEmotionMessageRes = Record<string, never>
export type SendVideoMessageRes = Record<string, never>
export type SendFileMessageRes = Record<string, never>
export type SendLinkMessageRes = Record<string, never>
export type SendMpvVideoMessageRes = Record<string, never>
export type SendMediaMessageRes = Record<string, never>

// 4) 素材

export enum ContentItemType {
  Article = 1,
  File = 2,
  Link = 7,
  Poster = 10,
  Media = 13,
  Channels = 104,
  Text = 300,
}


export enum ContentItemMediaType {
  Image = 1,
  Audio = 2,
  Video = 3,
  Voice = 4
}



export interface ContentItem {
  id: number | string;
  title?: string;
  img?: string;
  desc?:string;
  type?: ContentItemType; // 1文章 2文件 7链接 10海报 13 多媒体() 104视频号 300文本。一次只能查一个类型
  create_time:number //毫秒数
  status?: number; // 0未开始 1进行中 2已结束
  files_type?: number; // 文件类型（仅文件有） 1.excel 2.pdf 3.word 4.ppt
  h5_Url?: string;
  applets_url?: string;
  media_type?: ContentItemMediaType; // 多媒体素材类型（仅多媒体有） 1图片 2音频 3视频 4语音
  content?: string; // 文本内容(仅新文本素材)
  voice_time?: number; // 语音时长
  sphfeed_cover_url?: string;
  sphfeed_avatar?: string;
  sphfeed_desc?: string;
  sphfeed_nickname?: string;
  file_size?: number;
  md5?: string;
  image_width?: number;
  image_height?: number;
  video_image_size?: number;
  video_time?: number;
  video_image_md5?: string;
  video_img?: string;
}
export interface ContentListRes
{
  total: number,
  size: number,
  current: number,
  pages: number,
  records: ContentItem[]
}
