/**
 * Callback types for haogu SCRM -> AI staff topics
 * Generated from the request: work complete, order placed, read marks, link read, message, customer event
 */

/** 设备信息 */
export interface DeviceInfo {
	mac?: string;
	os?: string;
	osVer?: string;
	net?: string;
	width?: string;
	height?: string;
	browser?: string;
	browserVer?: string;
	ipAddr?: string;
	area?: string;
	userAgent?: string;
	brand?: string;
}

/** 5.1 作业完成回调 */
export interface ScrmWorkToAiStaff {
	/** 客户 unifiedId，使用 string 以兼容较大数值 */
	custUnifiedUserId: string;
	link: string;
	/** 状态，当前只有 1 表示完成 */
	status: number;
	score:number
}

/** 5.2 用户成单回调 */
export interface ScrmOrderPlacedToAiStaff {
	orderId: string;
	unifiedUserId: string;
	createDate: number;
}

/** 5.3 IM 客户消息已读回调 */
export interface ScrmReadMarkToAiStaff {
	staffId: number;
	custUnifiedUserId: string;
	lastReadTime: number;
}

/** 5.4 链接消息已读回调 */
export interface ScrmLinkReadMarkToAiStaff {
	staffId: number;
	custUnifiedUserId: string;
	shareUuid: string;
	url: string;
	/** 素材类型 */
	type: number;
	/** 素材子类型 */
	mediaType?: number;
	/** 素材 id */
	contentId: number;
	/** 在这个时间点已读, 时间戳(ms) */
	lastReadTime: number;
	/** 阅读时长(s) */
	duration: number;
	deviceInfo?: DeviceInfo;
}

/** 5.5 消息回调给数字员工
 *  注：调用方字段风格可能为驼峰或下划线，这里使用宽松结构以兼容两种风格。
 */

// 文本: 11041
// 图片: 11042
// 视频: 11043
// 语音: 11044
// 文件: 11045
// 位置: 11046
// 链接: 11047
// 表情: 11048
// 红包: 11049
// 名片: 11050
// 小程序: 11066
// 视频号: 101010
// 表情包: 110490
export enum ScrmMessageType {
	text = 11041,
	image = 11042,
	video = 11043,
	voice = 11044,
	file = 11045,
	location = 11046,
	link = 11047,
	emotion = 11048,
	redPacket = 11049,
	card = 11050,
	miniProgram = 11066,
	videoChannel = 101010,
	emotionPacket = 110490
}
export interface ScrmMessageToAiStaff {
	[key: string]: any;
	content?:string
	conversationId:string
	receiver:string
	sender:string
	serverId:string
	msgTime:number
	filePath?:string

	type:ScrmMessageType
	// 1:是，0:否
	isWithdraw: 1 | 0
	// 1:是，0:否
	isRoom: 1 | 0
	// 1:是，0:否
	isStaff?: 1 | 0

	cloudCustomData?:string
}

export interface CloudCustomData {
	fromAi:string
}

/** 5.6 / 5.7 好友通过 / 好友删除回调（同 topic） */
export interface ScrmCustomerEventToAiStaff {
	enterpriseId: number;
	staffId: number;
	staffToolUserId: string;
	custUnifiedUserId: string;
	custToolUserId: string;
	conversationId: string;
	/** 状态：删除: 0,8,2049；添加: 9,2057,2313 */
	status: number;
	/** 客户的 unionid */
	custUnionId: string;

	custNickname: string;
}

/** Union type covering all known callback payloads */
export type HaoguCallbackPayload =
	| ScrmWorkToAiStaff
	| ScrmOrderPlacedToAiStaff
	| ScrmReadMarkToAiStaff
	| ScrmLinkReadMarkToAiStaff
	| ScrmMessageToAiStaff
	| ScrmCustomerEventToAiStaff;
