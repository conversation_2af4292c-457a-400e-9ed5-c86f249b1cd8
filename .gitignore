# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules
.pnp
.pnp.js
*.js.map

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist


# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem
build/
tmp/
temp/
nohup.out
audio/
*.out
logs/
dev/
*.log
/feishu/
.aider*
data/
event
.idea

chat_history.json

prisma_client
package-lock.json
yarn.lock
pnpm-lock.yaml