'use client'

import { useState } from 'react'
import MaterialShow from '../component/material/materialShow'
import HaoGuMaterialSearch from '../component/material/haoguMaterialSearch'

export default function MaterialPage() {
  const [activeTab, setActiveTab] = useState<'local' | 'search'>('local')

  return (
    <div>
      {/* 标签切换 */}
      <div className="tabs tabs-boxed mb-6">
        <button
          className={`tab ${activeTab === 'local' ? 'tab-active' : ''}`}
          onClick={() => setActiveTab('local')}
        >
          本地素材库
        </button>
        <button
          className={`tab ${activeTab === 'search' ? 'tab-active' : ''}`}
          onClick={() => setActiveTab('search')}
        >
          HaoGu素材搜索
        </button>
      </div>

      {/* 内容区域 */}
      {activeTab === 'local' ? <MaterialShow /> : <HaoGuMaterialSearch />}
    </div>
  )
}