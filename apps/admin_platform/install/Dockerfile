# syntax=docker.io/docker/dockerfile:1

FROM node:24-alpine AS base

# Install dependencies only when needed
FROM base
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
RUN corepack enable && corepack prepare pnpm@latest --activate && pnpm config set registry https://registry.npmmirror.com && npm config set registry https://registry.npmmirror.com
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY pnpm-workspace.yaml package.json ./

# 复制所有 package.json 文件，保持目录结构
COPY apps/admin_platform/package.json ./apps/admin_platform/
COPY apps/moer_overseas/package.json ./apps/moer_overseas/
COPY apps/yuhe/package.json ./apps/yuhe/
COPY apps/haogu/package.json ./apps/haogu/
COPY packages/config/package.json ./packages/config/
COPY packages/lib/package.json ./packages/lib/
COPY packages/model/package.json ./packages/model/
COPY packages/service/package.json ./packages/service/

RUN pnpm install

COPY . .

RUN pnpm dlx turbo prisma_generate

WORKDIR /app/apps/admin_platform

RUN pnpm run build

ENTRYPOINT ["pnpm","run","start"]