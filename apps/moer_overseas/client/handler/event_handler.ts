import { ObjectUtil } from 'lib/object'
import logger from 'model/logger/logger'
import { IChattingFlag } from '../../state/user_flags'
import { AsyncLock } from 'model/lock/lock'
import { Config } from 'config'
import { getUserId } from 'config/chat_id'
import { DataService } from '../../helper/getter/get_data'
import { sleep } from 'lib/schedule/schedule'
import { getState } from 'service/llm/state'
import { MoerOverseasAPI } from 'model/moer_overseas_api/moer_overseas_api'
import { catchError } from 'lib/error/catchError'
import { PrismaMongoClient } from '../../helper/mongodb/prisma'
import { ContextBuilder } from '../../workflow/context'
import { PreCourseCompletionTask } from './task/pre_course_completion'
import { EnergyTest } from '../../workflow/nodes/energy_test'
import { HumanTransfer } from '../../human_transfer/human_transfer'
import { UserLanguage } from '../../helper/language/user_language_verify'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../../service/base_instance'
import { replyClient, yCloudCommonMessageSender, yCloudMessageSender } from '../../service/instance'
import { IScheduleTime } from '../../helper/tool/creat_schedule_task'
import { Node } from 'service/agent/workflow'
import { HumanTransferType } from 'service/human_transfer/human_transfer'
import { SendWelcomeMessageTask } from './task/send_welcome_message_task'

export interface IMoerEvent {
  event: string
  [key: string]: any
}

export interface IRumengyingOrderEvent {
  logid: string // 日志 ID
  event: string // 事件类型
  userId: number // 客户 ID
  mobile: string // 手机号
  nationcode: string
  goodsName: string // 商品名称
  sku: string // 商品 SKU
  stage: number // 阶段
  simpleName: string // 老师名称
  simpleId: number // 老师 ID
  language: string //偏好语言
  name: string
}


interface ILiveStreamEvent {
  channelId: string          // 频道号
  groupId?: string           // 分组id，非必传
  viewerId: string           // 参会人ID
  nickName?: string          // 客户昵称
  logType: number            // 日志类型
  interactType: string       // 日志类型对应枚举
  logTime: number            // 日志时间戳
  ipAddress?: string         // IP地址，非必传
  userAgent?: string         // UA信息，非必传
  referer?: string           // referer信息 - 请求来源，非必传
  viewerCount: number        // 当前参会人数
  timestamp: number          // 13位毫秒级时间戳
  sessionId: string | null   // 当前场次
  userOrigin?: string        // 客户来源，非必传
  content?: string           // logType为101和102时，表示学员名单的oss地址
  role: 'teacher' | 'guest' | 'viewer' | 'assistant' | 'attendee' | 'listener' // 客户身份信息
  inClass?: number           // 频道是否在直播中，1表示正在直播，2表示不在直播
  status: number             // 状态码（可能表示成功或失败的标志）
  websocketId: string        // websocket连接id，用于计算参与直播时长
  event: string
}

export interface EnergyTestEvent {
  logid: string
  examScore: number
  userId: number | string
  mobile?: string
  nationcode?: string // 国家码
  event: string
  project?: string
}

export interface PaymentEvent {
  sku: string
  userId: number | string
  event: string
}

interface IMergeUserEvent {
  event: string
  oldUserId: string
  oldMobile: string
  newUserId: string
  newMobile: string
  project: string
}

export class MoerEventHandler {
  private static moerIdMap: Map<string, string> | undefined
  public static async handle(event: IMoerEvent) {
    console.log(JSON.stringify(event, null, 4))

    if (ObjectUtil.isEmptyObject(event)) {
      // 忽略空事件
      return
    }

    try {
      switch (event.event) {
        // 先导课完课
        case 'course_study_guide':
          this.handlePreCourseComplete(event)
          break

        // 能量测评完成
        case 'jinshuju_user_exam_score':
          this.handleCompleteEnergyTest(event as EnergyTestEvent)
          break

        // 录播课完课
        case 'course_study_review':
          this.handleCourseComplete(event)
          break

        // 付款
        case 'course_pay_paid':
          logger.log(JSON.stringify(event, null, 4))
          this.handlePaidCourse(event as PaymentEvent)
          break

        // 付款失败
        case 'course_pay_unpaid':
          logger.log(JSON.stringify(event, null, 4))
          this.handlePaymentFailure(event as PaymentEvent)
          break

        // 进入直播间，离开直播间
        case 'live_stream_status':
          this.handleLiveStreamStatus(event as ILiveStreamEvent)
          break
        case 'rumengying_order':
          this.handleRumenyingOrder(event as IRumengyingOrderEvent)
          break

        case 'merge_user':
          this.handleMergeUser(event as IMergeUserEvent)
          break

        default:
          this.handleUnknownEvent(event)
          break
      }
    } catch (e) {
      logger.error('moer event handler error:', e)
    }
  }

  static async handlePreCourseComplete(event: IMoerEvent) {
    // logger.log(JSON.stringify(event, null, 4))
    //
    // const chat = await chatDBClient.getByMoerId(event.userId.toString())
    // if (!chat) {
    //   return
    // }
    //
    //
    // await chatStateStoreClient.update(chat.id, {
    //   state: <IChattingFlag>{
    //     is_complete_pre_course: true,
    //   },
    // })
    //
    // // 加个锁，防止跟对话冲突
    // const lock = new AsyncLock()
    //
    // await lock.acquire(chat.id, async () => {
    //   await PreCourseCompletionTask.sendPreCourseCompleteGift(chat.id)
    // }, { timeout: 3 * 60 * 1000 }) // 时间设长点，防止任务失效


  }

  public static async handleCompleteEnergyTest(event: EnergyTestEvent) {
    logger.log(JSON.stringify(event, null, 4))

    const eventData = event
    const chatInfo = await chatDBClient.getByMoerId(event.userId.toString())
    if (!chatInfo) {
      logger.warn('MoerId 未绑定')
      return
    }


    if (!Config.isOnlineTestAccount() && (await chatStateStoreClient.getFlags<IChattingFlag>(chatInfo.id)).is_complete_energy_test) {
      return
    }

    await chatStateStoreClient.update(chatInfo.id, {
      state:<IChattingFlag> {
        is_complete_energy_test: true,
        energy_test_score: eventData.examScore,
      },
    })

    const userId = getUserId(chatInfo.id)

    // 加个锁，防止跟对话冲突
    const lock = new AsyncLock()

    await lock.acquire(chatInfo.id, async () => {
      await sleep(60 * 1000)

      await EnergyTest.invoke(await getState(chatInfo.id, userId))
    }, { timeout: 3 * 60 * 1000 }) // 时间设长点，防止任务失效

  }

  public static handleCourseComplete(event: IMoerEvent) {
    // logger.debug('完课:', JSON.stringify(event, null, 4))
  }

  public static async handlePaidCourse(event: PaymentEvent) {
    logger.debug('已付款:', JSON.stringify(event, null, 4))
    const chatUser = await chatDBClient.getByMoerId(event.userId.toString())
    if (!chatUser) {
      return
    }

    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chatUser.id)).is_complete_payment) {
      return
    }

    await chatStateStoreClient.update(chatUser.id, {
      state:<IChattingFlag> {
        is_complete_payment: true,
      },
      nextStage: Node.PostSale,
    })
    const userId = getUserId(chatUser.id)

    const language = await UserLanguage.getLanguage(chatUser.id)
    let msg = ''
    if (language === UserLanguage.Language_EN) {
      msg = 'Congratulations on joining the System Course, and thank you for your dedication! What\'s our shipping address so we can send you the mat?'
    } else if (language === UserLanguage.Language_ZH) {
      msg = '恭喜同學加入系統課，感恩精進，咱們收件地址是哪裏，給咱們寄墊子。'
    }

    await yCloudMessageSender.sendById({
      chat_id: chatUser.id,
      ai_msg: msg,
      type:'text'
    })

    await HumanTransfer.transfer(chatUser.id, userId, HumanTransferType.PaidCourse, true)
  }

  public static async handlePaymentFailure(event: IMoerEvent) {
    logger.debug('付款失败:', JSON.stringify(event, null, 4))
    const chatUser = await chatDBClient.getByMoerId(event.userId.toString())
    if (!chatUser) {
      return
    }

    // 如果已付款，不再处理
    if (await DataService.isPaidSystemCourse(chatUser.id)) {
      return
    }

    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chatUser.id)).handled_failed_payment) {
      const languageOption = await UserLanguage.getLanguageSetting(chatUser.id)
      await chatHistoryServiceClient.addUserMessage(chatUser.id, '下单失败')
      const state = await getState(chatUser.id, getUserId(chatUser.id))
      const context = await ContextBuilder.build({
        state: state,
        customerChatRounds: 0,
        talkStrategyPrompt: `客户当前正在犹豫是否下单，后台看到了下单失败。可以询问客户：班班在后台看到了您的订单，但还未支付成功哦，咱这边是遇到什么问题了么
${languageOption}`
      })

      await replyClient.invoke({
        state: state,
        promptName: 'failed_payment_followup',
        context: context
      })

      return
    }

    await chatStateStoreClient.update(chatUser.id, {
      state: <IChattingFlag>{
        handled_failed_payment: true,
      },
    })

    const userId = getUserId(chatUser.id)

    await HumanTransfer.transfer(chatUser.id, userId, HumanTransferType.HesitatePayment)
  }

  public static async handleRumenyingOrder(event: IRumengyingOrderEvent): Promise<void> {

    const phoneNumber = `${event.nationcode}${event.mobile}`

    const chatId = `+${phoneNumber}_${Config.setting.wechatConfig?.id}`


    await SendWelcomeMessageTask.newCourseUser(chatId, phoneNumber, event.name)

    await yCloudCommonMessageSender.sendYCloudTemplate(chatId, {
      description: '[主动触达客户消息]',
      language: await UserLanguage.getLanguage(chatId) === UserLanguage.Language_ZH ? 'zh_HK' : 'en',
      templateName: await UserLanguage.getLanguage(chatId) === UserLanguage.Language_ZH ? 'f_v1c_contact' : 'f_v1e_contact',
      variable: [],
    })
  }

  public static async handleLiveStreamStatus(event: ILiveStreamEvent) {
    const chatId = await DataService.getChatIdByMoerId(event.viewerId)
    if (!chatId) {
      return
    }

    const currentTime = await DataService.getCurrentTime(chatId)
    if (!currentTime.is_course_week) {
      return
    }

    let isInLiveStreamPattern = ''
    let isInClass = ''
    let isNotified = ''
    switch (currentTime.day) {
      case 1:
        isInLiveStreamPattern = 'is_in_day1_class_live_stream'
        isInClass = 'is_in_day1_class'
        isNotified = 'is_day1_login_out_notified'
        break
      case 2:
        isInLiveStreamPattern = 'is_in_day2_class_live_stream'
        isInClass = 'is_in_day2_class'
        isNotified = 'is_day2_login_out_notified'
        break
      case 3:
        isInLiveStreamPattern = 'is_in_day3_class_live_stream'
        isInClass = 'is_in_day3_class'
        isNotified = 'is_day3_login_out_notified'
        break
      case 4:
        isInLiveStreamPattern = 'is_in_day4_class_live_stream'
        isInClass = 'is_in_day4_class'
        isNotified = 'is_day4_login_out_notified'
        break
      default:
        break
    }

    if (event.interactType === 'login') {
      await chatStateStoreClient.update(chatId, {
        state: <IChattingFlag>{
          [isInLiveStreamPattern]: true,
          [isInClass]: true,
        },
      })

    } else if (event.interactType === 'diconnect') {
      await chatStateStoreClient.update(chatId, {
        state: <IChattingFlag>{
          [isInLiveStreamPattern]: false,
        },
      })

    }

    const userId = getUserId(chatId)

    // 掉线通知 (海外版本不需要)
    // 掉线 90s 通知一次，只通知一次
    // 只通知，不转人工
    // 只在上课时间做校验
    // if (await DataService.isWithinClassTime(currentTime) && !await DataService.isPaidSystemCourse(chatId)) {
    //   if (chatStateStoreClient.getFlags(chatId)[isInLiveStreamPattern] === false) {
    //     await SilentReAsk.schedule(chatId, async () => {
    //       // 加锁来判断
    //       const lock = new AsyncLock()
    //       await lock.acquire('groupMessage', async () => {
    //         if (chatStateStoreClient.getFlags(chatId)[isNotified]) { // 只发一次通知
    //           return
    //         }
    //
    //         // 如果还在线，退出
    //         if (chatStateStoreClient.getFlags(chatId)[isInLiveStreamPattern]) {
    //           return
    //         }
    //
    //         // AI 通知
    //         await LogOutNotification.notify(chatId, userId, currentTime)
    //
    //         chatStateStoreClient.update(chatId, {
    //           state: {
    //             [isNotified]: true
    //           }
    //         })
    //       })
    //
    //     },  90 * 1000)
    //   }
    // }
  }

  public static handleUnknownEvent(event: IMoerEvent) {
    logger.debug('unknown moer event:', JSON.stringify(event))
  }

  private static async handleMergeUser(event: IMergeUserEvent) {
    // 更新 moerId, 手机号，课程期数
    const chat = await chatDBClient.getByMoerId(event.oldUserId)
    if (!chat) {
      return
    }

    let courseNo = chat.course_no
    const [error, user] = await catchError(MoerOverseasAPI.getUserByPhone(event.newMobile))
    if (user) {
      courseNo =  DataService.parseCourseNo(user)
    }

    await PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chat.id
      },
      data: {
        moer_id: event.newUserId,
        course_no: courseNo,
      }
    })

    await chatStateStoreClient.update(chat.id, {
      state: <IChattingFlag>{
        phoneNumber: event.newMobile
      }
    })

  }
}

export class LogOutNotification {

  //   static async notify(chatId: string, userId: string, currentTime: IScheduleTime) {
  //     if (!currentTime.is_course_week || currentTime.day > 4) {
  //       return
  //     }
  //
  //     const userSlotsPainPoints = await new ContextBuilder({ state }).customerPortrait(chatId)
  //
  //     // 根据天数，返回对应信息
  //     const courseContentSummary = {
  //       1: '第一课：情绪减压，解析情绪和睡眠问题的成因和处理方式，再从手把手教冥想入门姿势。最带练对情绪和睡眠非常有帮助冥想《沉浸式秒睡》',
  //       2: '第二课：财富唤醒，聚焦【财富问题】，解读什么是富足和吸引力法则，帮助大家一步步找到自己负债、迷茫的内在原因。通过【财富果园】的冥想带练，帮助大家扫除对于物质、财富的内心阻碍，养成更加容易获取财富、打理财富的思维习惯',
  //       3: '第三课：红靴子是老师最重磅的课程，老师会带练【红靴子飞跃】冥想，主要聚焦于大家专注力、内在能量的提升。老师说如何一生只练一个冥想，就练习红靴子吧。这堂课结束的时候来时会和想精进学习的人说明21天的系统课内容',
  //       4: '第三课：唐宁老师会传授只有线下才会开设的蓝鹰预演，帮助我们更好地掌控自己的情绪和思维，认识自己、明确目标，并激发内在的力量，提高振动频率，以实现梦想，也能更从容地应对新挑战与困难。同时还会学习无限数息法延长呼吸，如何深呼吸延长几倍',
  //     }
  //
  //     // 不同天数，返回不同的话术，避免重复
  //     const logOutNotificationPrefix = {
  //       1: '同学，这边看到你掉线了，有什么问题么?',
  //       2: 'Hi,  刚刚看到不在直播间了。需要帮助吗？',
  //       3: '同学，看到掉线了，是网络出问题了吗?',
  //       4: '同学咱这边掉线了，是有什么问题吗?',
  //     }
  //
  //     if (StringHelper.isEmpty(userSlotsPainPoints)) {
  //       await this.sendDefaultNotify(chatId, userId, currentTime)
  //       return
  //     }
  //
  //     const llmRes = await LLM.predict(`结合客户的情况和今天冥想课程的内容，提醒掉线的客户回到直播课程。
  // 如果相关性不强，可以多推理几步，找一下间接原因，从而结合上客户的情况。
  //
  // 客户信息：
  // ${userSlotsPainPoints}
  //
  // 课程内容：
  // ${courseContentSummary[currentTime.day]}
  //
  // 例如：${logOutNotificationPrefix[currentTime.day]} 今天老师会针对你提到过的 xxx 讲 xxx，希望你听完呢
  //
  // 以“${logOutNotificationPrefix[currentTime.day]}”开始，“希望你听完呢”结束，简洁清晰。`)
  //
  //     await YCloudMessageSender.sendById({
  //       chat_id: chatId,
  //       ai_msg: llmRes,
  //       type:'text'
  //     })
  //   }

  private static async sendDefaultNotify(chatId: string, userId: string, currentTime: IScheduleTime) {
    const logOutNotification = {
      1: '同学，这边看到你掉线了，有什么问题么? 今天老师会针对你提到过的情绪波动和睡眠困扰，讲解情绪管理的方法，并带练一个对改善睡眠非常有效的冥想《沉浸式秒睡》，希望你听完呢。',
      2: 'Hi，刚刚看到不在直播间了。需要帮助吗？今天老师会通过【财富果园】的冥想带练，讲解如何扫除内心对于物质和财富的阻碍。这可能会帮助你更好地梳理关于负债和迷茫的问题，希望你听完呢！',
      3: '同学，看到掉线了，是网络出问题了吗？今天老师会带大家练【红靴子飞跃】冥想，这个课程可以帮助提升专注力和内在能量，我记得你之前提到希望提高工作时的专注状态，这节课非常适合你哦，希望你听完呢。',
      4: '同学咱这边掉线了，是有什么问题吗? 第三课：唐宁老师会传授只有线下才会开设的蓝鹰预演，帮助我们更好地掌控自己的情绪和思维，认识自己、明确目标，并激发内在的力量，提高振动频率，以实现梦想，也能更从容地应对新挑战与困难。同时还会学习无限数息法延长呼吸，如何深呼吸延长几倍',
    }
    await yCloudMessageSender.sendById({
      chat_id: chatId,
      ai_msg: logOutNotification[currentTime.day],
      type:'text'
    })
  }
}

