import express from 'express'
import { init } from './bootstrap'
import { catchGlobalError } from 'model/server/server'
import { register } from 'lib/prometheus_client'
import logger from 'model/logger/logger'
import { override } from '../config/override'
import { manifest } from '../config/manifest'
import { receiveMessageCount } from '../prometheus/client'
import { BigPlanner } from '../planner/daily_plan/big_planner'

async function main() {
  const ctx = await init()

  const app = express()
  app.use(express.json())

  app.get('/', (req, res) => {
    logger.log('Hello Client, this is Server!')
    res.send('ok')
  })

  app.post('/message', async (req, res) => {
    ctx.messageHandler.handle(req.body)

    receiveMessageCount.labels({ bot_id: (manifest.projectName || 'unknown') }).inc(1)
    res.send('ok')
  })

  app.post('/finish_work', async(req, res) => {
    override.handleFinishWork(req.body)
    res.send('ok')
  })

  app.post('/order', async(req, res) => {
    override.handleOrder(req.body)
    res.send('ok')
  })

  app.post('/event', async (req, res) => {
  // 接收消息
    const data = req.body

    override.handle(data)
    res.send('ok')
  })

  app.post('/read_link', async(req, res) => {
    override.handleReadLink(req.body)
    res.send('ok')
  })

  app.post('/read_message', async(req, res) => {
    override.handleReadMessag(req.body)
    res.send('ok')
  })

  app.post('/new_customer', async(req, res) => {
    override.handleNewCustomer(req.body)
    res.send('ok')
  })

  app.get('/metrics', async (req, res) => {
    res.status(200).set('Content-Type', register.contentType).send(await register.metrics())
  })

  app.post('/clear_cache', async(req, res) => {
    await override.clearCache(req.body.chat_id)
    res.status(200).send('ok')
  })

  app.post('/big_planner', async(req, res) => {
    BigPlanner.plan(req.body.chat_id)
    res.status(200).send('ok')
  })

  ctx.messageHandler.startWorker()

  app.listen(ctx.port, '0.0.0.0', () => {
    console.log(`Server is running on port ${ctx.port}`)
  })

  catchGlobalError()
}

main().catch((e) => {
  console.error(e)
})