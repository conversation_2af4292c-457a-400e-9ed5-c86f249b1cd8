import { ClientAccountConfig, loadConfigByAccountName } from 'service/database/config'
import { Config } from 'config'
import chalk from 'chalk'
import { manifest } from '../config/manifest'
import { override } from '../config/override'
import { JuziMessageHandler } from 'service/message_handler/juzi/message_handler'
import { TaskRegister } from '../workflow/helper/register_task'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { haoguVisualizedSopProcessor } from '../config/instance/instance'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../config/instance/base_instance'
import { messageReplyServiceClient } from '../config/instance/message_replay_service_instance'
import { eventTrackClient } from '../config/instance/event_track_instance'
import { taskWorker } from '../config/instance/task_instance'

export async function init() {
  const name = process.env.WECHAT_NAME

  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  const account = await ClientAccountConfig.getAccountByName(name)
  if (!account) {
    console.error(`找不到${name}对应的账号`)
    process.exit(1)
  }


  // 初始化配置
  Config.setting.wechatConfig = await loadConfigByAccountName(name)
  Config.setting.startTime = Date.now()
  Config.setting.AGENT_NAME = manifest.agentName
  Config.setting.projectName = manifest.projectName

  console.log(
    chalk.green(
      `当前账号：${chalk.bold(Config.setting.wechatConfig?.name)}`,
    ),
  )

  // 注册所有任务函数
  TaskRegister.register()

  // 启动 SilentReAsk Worker
  SilentReAsk.startWorker()

  // SOP Worker
  haoguVisualizedSopProcessor.start()


  // const messageHandler = new HaoguMessageHandler({
  //   handleUnknownMessage: override.handleUnknownMessage,
  //   handleImageMessage: override.handleImageMessage,
  //   // handleTextAndImageMessage: override.handleTextAndImageMessage,
  //   handleVideoMessage: override.handleVideoMessage,
  //   sendWelcomeMessage: override.sendWelcomeMessage
  // },
  // chatDBClient, chatHistoryServiceClient, chatStateStoreClient, messageReplyServiceClient, eventTrackClient
  // )
  const messageHandler = new JuziMessageHandler({
    handleUnknownMessage: override.handleUnknownMessage,
    handleImageMessage: override.handleImageMessage,
    // handleTextAndImageMessage: override.handleTextAndImageMessage,
    handleVideoMessage: override.handleVideoMessage,
    sendWelcomeMessage: override.sendWelcomeMessage

  }, chatDBClient, chatHistoryServiceClient, chatStateStoreClient, messageReplyServiceClient, eventTrackClient)

  // 消息处理 Worker
  messageHandler.startWorker()

  // Planner Task Worker
  taskWorker.start()

  return {
    port: account.port,
    messageHandler,
  }
}