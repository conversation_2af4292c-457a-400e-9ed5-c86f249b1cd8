import { MemoryStore } from 'service/memory/memory_store'
import { LLMReply } from 'service/llm/llm_reply'
import { BaseHumanTransfer, HumanTransfer, HumanTransferType } from 'service/human_transfer/human_transfer'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from './base_instance'
import { commonMessageSender } from './send_message_instance'
import { eventTrackClient } from './event_track_instance'
import { HaoguVisualizedSopProcessor } from '../../visualized_sop/visualized_sop_processor'
import { manifest } from '../manifest'
import { ExtractUserSlots } from 'service/user_slots/user_slot'

export const memoryStoreClient = new MemoryStore(chatHistoryServiceClient, chatStateStoreClient)
export const llmReplyClient = new LLMReply(chatDBClient, chatHistoryServiceClient)
export const haoguVisualizedSopProcessor = new HaoguVisualizedSopProcessor(manifest.projectName, chatD<PERSON>lient, chatHistoryServiceClient, commonMessageSender)
// export const haoguVisualizedGroupSopProcessor = new HaoguVisualizedGroupSopProcessor(manifest.projectName, PrismaMongoClient.getCommonInstance(), haoguMessageSender)
export const humanTransferClient = new BaseHumanTransfer(chatDBClient, chatStateStoreClient)
export const humanTransfer = new HumanTransfer({ transferMessage: HumanTransferType, eventTracker: eventTrackClient, humanTransferClient })

export const extractUserSlots = new ExtractUserSlots({ chatHistoryServiceClient, chatStateStoreClient, topicRecommendations: manifest.extractUserSlots.topicRecommendations, topicRules: manifest.extractUserSlots.topicRules })