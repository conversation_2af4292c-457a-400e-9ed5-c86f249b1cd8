import logger from 'model/logger/logger'
import { chatHistoryServiceClient } from './base_instance'
import { commonMessageSender } from './send_message_instance'
import { ContextBuilder } from '../../workflow/context'
import { getPrompt } from 'service/agent/prompt'
import { getState } from 'service/llm/state'
import { getUserId } from 'config/chat_id'
import { LLM } from 'lib/ai/llm/llm_model'
import { TaskWorker } from 'service/task/task_worker'
import { UUID } from 'lib/uuid/uuid'
import { manifest } from '../manifest'

export const taskWorker = new TaskWorker(async (chat_id: string, mainTask: string) => {
  const freeKickPrompt = await getPrompt('free-kick')
  const contextComponent = new ContextBuilder({ state: await getState(chat_id, getUserId(chat_id)) })
  const dialogHistory = await chatHistoryServiceClient.getDialogHistory(chat_id, 2, 6, false)
  const metaActions = ''
  const availableMaterials = ''
  const retrievedKnowledge = ''
  const round_id = UUID.v4()

  const output = await LLM.predict(
    freeKickPrompt, {
      responseJSON: true,
      meta: {
        promptName: 'free_kick',
        chat_id: chat_id,
        round_id: round_id
      } }, {
      courseConfig: await contextComponent.courseConfig(),
      metaActions: metaActions,
      availableMaterials: availableMaterials,
      retrievedKnowledge: retrievedKnowledge,
      customerBehavior: await contextComponent.customerBehavior(chat_id),
      customerPortrait: await contextComponent.customerPortrait(chat_id),
      dialogHistory: dialogHistory,
      temporalInformation: await contextComponent.temporalInformation(chat_id),
      mainTask: mainTask,
    })
  let think: string = ''
  let activate: boolean = false
  let action: string[] = []
  let material: string[] = []
  let content: string = ''

  try {
    const parsedOutput = JSON.parse(output)
    think = parsedOutput.think
    activate = parsedOutput.activate
    action = parsedOutput.action
    material = parsedOutput.material
    content = parsedOutput.content
  } catch (error) {
    logger.error('FreeThink 解析 JSON 失败:', error)
  }
  logger.debug({ chat_id: chat_id }, `think: ${think}
activate: ${activate}
action: ${JSON.stringify(action)}
material: ${JSON.stringify(material)}
content: ${content}`)

  if (activate) {
    // const splitSentence = LLMNode.splitIntoSentencesWithMaxSentences(content, 2)
    // await commonMessageSender.sendMsg(chat_id, [{
    //   type: SendMessageType.text,
    //   text: content,
    //   description: ''
    // }], { roundId: round_id })
    await commonMessageSender.sendText(chat_id, {
      text: content,
      description: ''
    }, { roundId: round_id })
  }
}, manifest.projectName)
