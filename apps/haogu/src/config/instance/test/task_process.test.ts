import { TaskWorker } from 'service/task/task_worker'
import { getPrompt } from 'service/agent/prompt'
import { ContextBuilder } from '../../../workflow/context'
import { getState } from 'service/llm/state'
import { getUserId } from 'config/chat_id'
import { chatHistoryServiceClient } from '../base_instance'
import { UUID } from 'lib/uuid/uuid'
import { LLM } from 'lib/ai/llm/llm_model'
import logger from 'model/logger/logger'
import { commonMessageSender } from '../send_message_instance'
import { manifest } from '../../manifest'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const taskWorker = new TaskWorker(async (chat_id: string, mainTask: string) => {
      const freeKickPrompt = await getPrompt('free-kick')
      const contextComponent = new ContextBuilder({ state: await getState(chat_id, getUserId(chat_id)) })
      const dialogHistory = await chatHistoryServiceClient.getDialogHistory(chat_id, 2, 6, false)
      const metaActions = ''
      const availableMaterials = ''
      const retrievedKnowledge = ''
      const round_id = UUID.v4()

      const output = await LLM.predict(
        freeKickPrompt, {
          responseJSON: true,
          meta: {
            promptName: 'free_kick',
            chat_id: chat_id,
            round_id: round_id
          } }, {
          courseConfig: await contextComponent.courseConfig(),
          metaActions: metaActions,
          availableMaterials: availableMaterials,
          retrievedKnowledge: retrievedKnowledge,
          customerBehavior: await contextComponent.customerBehavior(chat_id),
          customerPortrait: await contextComponent.customerPortrait(chat_id),
          dialogHistory: dialogHistory,
          temporalInformation: await contextComponent.temporalInformation(chat_id),
          mainTask: mainTask,
        })
      let think: string = ''
      let activate: boolean = false
      let action: string[] = []
      let material: string[] = []
      let content: string = ''

      try {
        const parsedOutput = JSON.parse(output)
        think = parsedOutput.think
        activate = parsedOutput.activate
        action = parsedOutput.action
        material = parsedOutput.material
        content = parsedOutput.content
      } catch (error) {
        logger.error('FreeThink 解析 JSON 失败:', error)
      }
      logger.debug({ chat_id: chat_id }, `think: ${think}
activate: ${activate}
action: ${JSON.stringify(action)}
material: ${JSON.stringify(material)}
content: ${content}`)

      if (activate) {
        // const splitSentence = LLMNode.splitIntoSentencesWithMaxSentences(content, 2)
        // await commonMessageSender.sendMsg(chat_id, [{
        //   type: SendMessageType.text,
        //   text: content,
        //   description: ''
        // }], { roundId: round_id })
        await commonMessageSender.sendText(chat_id, {
          text: content,
          description: ''
        }, { roundId: round_id })
      }
    }, manifest.projectName)

    await taskWorker.processTask('8332403987279874033_2300', '引导观看回放: 发送Day1/Day2精选回放直达与3条要点，优先建议先看“双线合一”关键片段（≤15分钟）, 发送相关素材: 附上Day2多空趋势线指标领取与安装指引链接')

  })
})