import { PromptTemplate } from '@langchain/core/prompts'
import { LLM } from 'lib/ai/llm/llm_model'
import { MaterialManager } from '../helper/material_manager'
import { sleep } from 'openai/core'
import { commonMessageSender } from '../../config/instance/send_message_instance'
import { ActionInfo } from 'service/agent/stage'
import { DataService } from '../helper/get_data'


export class SendMaterialAction {


  public static async sendMaterial(chat_id:string, round_id:string, strategy: string | string[]) {
    const prompt = `# 角色设定
你负责分析理解本轮对话策略，你需要在素材库选取不超过3条最匹配的素材以支撑本轮对话策略的执行。这项任务需要你深刻理解本轮对话策略，分析当前客户所处的时间阶段。

# 本轮对话策略
{strategy}

# 时间信息
{timeInfo}

# 素材目录结构
 - 公司介绍相关（可能包含的素材名称：公司好评案例）
 - 6节课程服务相关（可能包含的素材标题：预习课1:《双线合一核心解析》、预习课2:《四点共振实战应用》、预习课3:《主力锁仓和出货》 、第x节预习视频、第x节课课后作业、第x节课课程笔记）
 - 交易体系相关
 - 工具相关(可能包含的素材标题：手机/电脑版APP安装方法、多空趋势线指标工具、抄底先锋指标工具、主力进出指标工具)
 - 3360实战班相关
 - 成交相关

# 以JSON格式输出
{{
  "selected_materials":[
    {{
      "category":"素材目录",
      "title":"素材标题"
    }}
  ]
}}
`



    const promptTemplate = PromptTemplate.fromTemplate(prompt)

    const timeInfo = await SendMaterialAction.getTimInfo(chat_id)

    const res = await LLM.predict(promptTemplate, {
      meta: {
        round_id: round_id,
        chat_id: chat_id
      },
      responseJSON:true,
      reasoningEffort:'low'
    }, { strategy, timeInfo })

    const jsonRes = JSON.parse(res)
    const selectedMaterials = jsonRes.selected_materials

    let caseInfo = ''
    const sourceIdList: string[] = []

    for (const selectedMaterial of selectedMaterials) {
      const material = await new MaterialManager().searchMaterialByTitle(chat_id, selectedMaterial.title, selectedMaterial.category)
      if (material && await new MaterialManager().isValidCourseMaterial(chat_id, material?.title)) {
        caseInfo += `${material.description}`
        sourceIdList.push(material.source_id)
      }
    }

    const callBack = async () => {
      for (const sourceId of sourceIdList) {
        await sleep(3000)
        await commonMessageSender.sendMaterial(chat_id, { sourceId: sourceId })
      }
    }

    return {
      guidance: caseInfo,
      callback: callBack
    } as ActionInfo

  }

  private static async  getTimInfo(chatId: string): Promise<string> {
    const currentTime = await DataService.getCurrentTime(chatId)

    if (currentTime.day < 1) {
      return '课前'
    } else if (currentTime.day > 6) {
      return '课后'
    } else {
      const dayNames = ['一', '二', '三', '四', '五', '六']
      return `当前课程第${dayNames[currentTime.day - 1]}天`
    }
  }

}