import { commonMessageSender } from '../../../config/instance/send_message_instance'
import { Config } from 'config'
import { MaterialManager } from '../material_manager'


describe('materialManagerTest', () => {

  it('sendMaterial', async () => {
    Config.setting.localTest = false
    await commonMessageSender.sendMaterial('242829731708929028_113', { sourceId:'166'  }, { force:true })
  }, 9e8)

  it('searchMaterial', async () => {
    const searchKey = ''
    const res = await new MaterialManager().searchMaterialByTitle('', searchKey, '工具相关')
    console.log(res)
  }, 60000)

})