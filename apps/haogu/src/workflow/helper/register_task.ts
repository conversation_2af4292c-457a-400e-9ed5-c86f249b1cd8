import { CommonTaskType, SilentReAsk } from 'service/schedule/silent_requestion'
import logger from 'model/logger/logger'
import { chatStateStoreClient } from '../../config/instance/base_instance'
import { commonMessageSender } from '../../config/instance/send_message_instance'
import { IChattingFlag } from '../../config/manifest'
import { sleep } from 'lib/schedule/schedule'
import { TaskManager } from 'service/task/task_manager'
import { Planner } from '../../planner'
import { BigPlanner } from '../../planner/daily_plan/big_planner'

export enum TaskName {
  TestTask = 'TestTask',
  SendCoreMaterials = 'SendCoreMaterials',
  SendInstallationGuide = 'SendInstallationGuide',
  SendPerfectEncourageMsg = 'SendPerfectEncourageMsg', // 发送满分鼓励消息
  SendHomeworkAnalysis = 'SendHomeworkAnalysis', // 发送作业解析
  SilenceDetection = 'SilenceDetection',
}

/**
 * 延迟任务
 */
export class TaskRegister {
  public static register() {
    // 注册测试任务
    SilentReAsk.registerTask(TaskName.TestTask, async (chat_id: string, params) => {
      logger.log(`执行测试任务 for chat: ${chat_id}`, params)
    })

    // 注册筹码峰核心资料任务(沉默5分钟等回复，10分钟直接发资料）
    SilentReAsk.registerTask(TaskName.SendCoreMaterials, async (chat_id: string, params) => {
      const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

      if (state.is_send_core_materials) {
        logger.log(`筹码峰核心资料已发送，跳过任务 for chat: ${chat_id}`)
        return
      }

      if (params === 5) { // 客户沉默5分钟
        await sleep(3000)
        await commonMessageSender.sendText(chat_id, {
          text: '哈咯，你忙好了吗？我把资料发你呢😊',
          description: '客户沉默5分钟等回复'
        })

        // 调度10分钟后的资料发送任务
        await SilentReAsk.schedule(
          TaskName.SendCoreMaterials,
          chat_id,
          5 * 60 * 1000, // 再等5分钟（总共10分钟）
          10,
          { auto_retry: true, independent: false }
        )
      } else { // 客户沉默10分钟
        await sleep(5000)
        await commonMessageSender.sendText(chat_id, {
          text: '🎁筹码峰核心资料：\n' +
              '【神奇的双线合一（选股）】https://ai.9635.com.cn/pw6wwafF【6分钟】 \n' +
              '【绝密的四点共振（买点）】https://ai.9635.com.cn/yjm8T2RA【7分钟】 \n' +
              '上面预习视频是教您如何使用无延迟筹码的，您看完跟我说哦，我来教您安装工具&调出筹码峰～',
          description: '客户沉默10分钟直接发筹码峰核心资料'
        })

        // 更新状态
        await chatStateStoreClient.update(chat_id, {
          state: <IChattingFlag>{
            is_send_core_materials: true
          }
        })

        // 调度安装教程任务（5分钟后）
        await SilentReAsk.schedule(
          TaskName.SendInstallationGuide,
          chat_id,
          5 * 60 * 1000,
          5,
          { auto_retry: true, independent: false }
        )

        logger.log(`筹码峰核心资料已发送，并调度安装教程任务 for chat: ${chat_id}`)
      }
    })



    // 注册安装教程任务
    SilentReAsk.registerTask(TaskName.SendInstallationGuide, async (chat_id: string, params) => {
      const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

      // 如果已经发送过，则不再发送
      if (state.is_send_installation_guide) {
        logger.log(`安装教程已发送，跳过任务 for chat: ${chat_id}`)
        return
      }

      if (params === 5) { // 客户沉默5分钟
        await sleep(3000)
        await commonMessageSender.sendText(chat_id, {
          text: '哈咯，你忙好了吗？我把安装教程发你呢😊',
          description: '客户沉默5分钟等回复'
        })

        // 调度10分钟后的安装教程发送任务
        await SilentReAsk.schedule(
          TaskName.SendInstallationGuide,
          chat_id,
          5 * 60 * 1000, // 再等5分钟（总共10分钟）
          10,
          { auto_retry: true, independent: false }
        )
      } else { // 客户沉默10分钟
        await sleep(5000)
        await commonMessageSender.sendText(chat_id, {
          text: '筹码峰如何安装？电脑和手机都可以。看下教程，有不懂的问我。接下来正式开课我也会通知你的，是在微信直播！ 👉视频教程： https://drive.weixin.qq.com/s?k=AGwAqAeQAA0d84u1Sv \n' +
              '​\n' +
              '手机版、电脑版下载链接： https://download.9635.com.cn/',
          description: '客户沉默10分钟直接发安装教程'
        })

        // 更新状态，标记安装教程已发送和欢迎语阶段完成
        await chatStateStoreClient.update(chat_id, {
          state: <IChattingFlag>{
            is_send_installation_guide: true,
            is_welcome_stage_completed: true
          }
        })

        logger.log(`安装教程已发送，欢迎语阶段完成 for chat: ${chat_id}`)
      }
    })

    // 注册满分鼓励消息任务
    SilentReAsk.registerTask(TaskName.SendPerfectEncourageMsg, async (chat_id: string, params) => {
      const { score, link } = params || {}

      logger.log(`发送满分鼓励消息 for chat: ${chat_id}, score: ${score}, link: ${link}`)

      // 发送满分鼓励消息
      await commonMessageSender.sendText(chat_id, {
        text: '刚刚在系统里看到咱们作业满分了！看得出来您对筹码知识的掌握非常扎实，这也是好人好股一直期望看到的。继续保持这样的学习状态，相信您一定能在股市中取得好成绩。',
        description: '满分作业鼓励消息'
      })

      logger.log(`满分鼓励消息已发送 for chat: ${chat_id}`)
    })

    // 注册作业解析任务
    SilentReAsk.registerTask(TaskName.SendHomeworkAnalysis, async (chat_id: string, params) => {
      const { score, link } = params || {}

      logger.log(`发送作业解析 for chat: ${chat_id}, score: ${score}, link: ${link}`)

      // 发送鼓励消息
      await commonMessageSender.sendText(chat_id, {
        text: '刚刚在系统看到您完成了作业，每一次练习都是进步的机会，继续加油！',
        description: '作业完成鼓励消息'
      })

      await sleep(2000) // 间隔2秒

      // 发送作业解析视频链接（暂时使用占位符）
      await commonMessageSender.sendText(chat_id, {
        text: '为了帮助您更好地理解题目，我给您准备了详细的作业解析视频：\n\n📹 作业解析视频：[作业解析视频链接占位符]\n\n您可以仔细对照一下，有任何疑问随时来问我哦！',
        description: '作业解析视频'
      })

      logger.log(`作业解析已发送 for chat: ${chat_id}`)
    })

    // 注册沉默检测任务
    SilentReAsk.registerTask(TaskName.SilenceDetection, async (chat_id: string) => {
      // 检查是否有 planner
      const currentTasks = await TaskManager.getFlexibleActiveTasks(chat_id)
      if (currentTasks.length) {
        const scheduledTasks = await Planner.taskSchedule(chat_id, currentTasks)
        // 合并需要立即处理的消息，直接处理
        await Planner.executeImmediateTask(chat_id, scheduledTasks)

        // 需要延后处理的消息
        await Planner.addDelayedTask(chat_id, scheduledTasks.filter((item) => item.scheduled_time !== 'now').map((item) => item.task_id))
      }
    })

    //注册 BigPlanner 每日任务
    SilentReAsk.registerTask(CommonTaskType.BigPlan, async (chat_id: string) => {
      await BigPlanner.plan(chat_id)
    })
  }
}


